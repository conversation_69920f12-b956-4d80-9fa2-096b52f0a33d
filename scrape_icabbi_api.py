import requests
from bs4 import BeautifulSoup
import markdown
import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import base64
try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

def setup_selenium_driver():
    """Setup Chrome WebDriver with appropriate options"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # Run in background
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

    try:
        if WEBDRIVER_MANAGER_AVAILABLE:
            # Use webdriver-manager to automatically download and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            # Fallback to system ChromeDriver
            driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error setting up Chrome driver: {e}")
        if not WEBDRIVER_MANAGER_AVAILABLE:
            print("Consider installing webdriver-manager: pip install webdriver-manager")
        print("Please make sure Chrome browser is installed")
        return None

def scrape_api_docs_with_selenium(base_url):
    """
    Scrape the iCabbi API documentation using Selenium to handle JavaScript and interactive elements
    """
    print(f"Scraping API documentation from {base_url}")

    driver = setup_selenium_driver()
    if not driver:
        return None

    try:
        # Load the main page
        driver.get(base_url)
        wait = WebDriverWait(driver, 10)

        # Wait for page to load
        time.sleep(3)

        # Initialize markdown content with API details
        md_content = """# iCabbi API Documentation

## API Details
- **API URL**: https://api.icabbidispatch.com/icd/
- **APP KEY**: 3ff0c443f405cae2ef723ccece2f8008e6a7a70a
- **SECRET KEY**: 27dd3a9574a6c72be5986aea31554921d687bcef
- **API Explorer**: https://api.icabbidispatch.com/docs/index.html

"""

        # Look for the resources_nav element
        try:
            resources_nav = driver.find_element(By.ID, "resources_nav")
            print("Found resources_nav element")

            # Get all API endpoint divs from the navigation (Swagger UI structure)
            endpoint_divs = resources_nav.find_elements(By.CSS_SELECTOR, "div[data-endpoint]")
            print(f"Found {len(endpoint_divs)} endpoint divs")

            # Create a directory for individual endpoint docs
            os.makedirs("api_endpoints", exist_ok=True)

            for i, endpoint_div in enumerate(endpoint_divs):
                try:
                    endpoint_name = endpoint_div.text.strip()
                    endpoint_id = endpoint_div.get_attribute('data-endpoint')

                    if not endpoint_name or not endpoint_id:
                        continue

                    print(f"Processing endpoint {i+1}/{len(endpoint_divs)}: {endpoint_name}")

                    # Click the endpoint div to navigate to it
                    driver.execute_script("arguments[0].click();", endpoint_div)
                    time.sleep(2)

                    # Extract endpoint documentation
                    endpoint_doc = extract_endpoint_documentation(driver, endpoint_name, endpoint_id)

                    # Add to main documentation
                    md_content += endpoint_doc['markdown']

                    # Save individual endpoint documentation
                    save_endpoint_doc(endpoint_doc, endpoint_name)

                    # Try to execute the "Try" button if available
                    try_response = execute_try_button(driver, endpoint_name, endpoint_id)
                    if try_response:
                        endpoint_doc['try_response'] = try_response
                        # Update the saved file with try response
                        save_endpoint_doc(endpoint_doc, endpoint_name)

                    time.sleep(1)  # Be nice to the server

                except Exception as e:
                    endpoint_name_safe = endpoint_div.text.strip() if endpoint_div else "unknown"
                    print(f"Error processing endpoint {endpoint_name_safe}: {e}")
                    continue

        except NoSuchElementException:
            print("Could not find resources_nav element. Trying alternative selectors...")
            # Try alternative navigation selectors
            nav_selectors = [
                ".nav-item", ".sidebar-item", ".api-nav",
                "#navigation", ".navigation", ".menu",
                "[role='navigation']", ".docs-nav"
            ]

            for selector in nav_selectors:
                try:
                    nav_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if nav_elements:
                        print(f"Found navigation using selector: {selector}")
                        # Process navigation elements
                        break
                except:
                    continue
            else:
                print("Could not find any navigation elements")
                # Extract whatever content is available
                content_elements = driver.find_elements(By.CSS_SELECTOR, ".content")
                for content in content_elements:
                    md_content += extract_content_to_markdown_selenium(content)

        return md_content

    except Exception as e:
        print(f"Error during scraping: {e}")
        return None
    finally:
        driver.quit()

def extract_endpoint_documentation(driver, endpoint_name, endpoint_id):
    """Extract documentation for a specific endpoint"""
    doc_data = {
        'name': endpoint_name,
        'endpoint_id': endpoint_id,
        'markdown': f"\n## {endpoint_name}\n\n",
        'parameters': [],
        'responses': [],
        'examples': []
    }

    try:
        # Look for content elements
        content_elements = driver.find_elements(By.CSS_SELECTOR, ".content")

        for content in content_elements:
            # Extract operation requirements and parameters
            doc_data['markdown'] += extract_content_to_markdown_selenium(content)

            # Look for parameter tables or lists
            param_tables = content.find_elements(By.CSS_SELECTOR, "table")
            for table in param_tables:
                doc_data['parameters'].extend(extract_table_data_selenium(table))

            # Look for response examples
            response_sections = content.find_elements(By.CSS_SELECTOR, ".response, .example-response")
            for response in response_sections:
                doc_data['responses'].append(response.text.strip())

        # Look for code examples
        code_blocks = driver.find_elements(By.CSS_SELECTOR, "pre, code, .code-example")
        for code in code_blocks:
            if code.text.strip():
                doc_data['examples'].append(code.text.strip())
                doc_data['markdown'] += f"\n```\n{code.text.strip()}\n```\n\n"

    except Exception as e:
        print(f"Error extracting documentation for {endpoint_name}: {e}")

    return doc_data

def execute_try_button(driver, endpoint_name, endpoint_id):
    """Execute the Try button for an endpoint and capture the response"""
    try_response = None

    try:
        # Look for Try button specific to this endpoint
        endpoint_selector = f"#{endpoint_id}"
        try_buttons = driver.find_elements(By.CSS_SELECTOR,
            f"{endpoint_selector} input[value='Try'], {endpoint_selector} .submit")

        if not try_buttons:
            # Try broader selectors
            try_buttons = driver.find_elements(By.CSS_SELECTOR, "input[value='Try']")
            # Filter to visible buttons
            try_buttons = [btn for btn in try_buttons if btn.is_displayed()]

        if try_buttons:
            print(f"Found Try button for {endpoint_name}")

            # Fill in sample data if there are input fields
            sample_data = get_sample_input_data(driver, endpoint_name, endpoint_id)

            # Click the Try button
            try_button = try_buttons[0]
            driver.execute_script("arguments[0].click();", try_button)

            # Wait for response
            time.sleep(3)

            # Look for modal popup with response
            modal_response = capture_modal_response(driver, endpoint_id)

            if modal_response:
                try_response = {
                    'sample_input': sample_data,
                    'response': modal_response,
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                print(f"Captured Try response for {endpoint_name}")
            else:
                print(f"No modal response found for {endpoint_name}")
        else:
            print(f"No Try button found for {endpoint_name}")

    except Exception as e:
        print(f"Error executing Try button for {endpoint_name}: {e}")

    return try_response

def get_sample_input_data(driver, endpoint_name, endpoint_id):
    """Generate or extract sample input data for the API endpoint"""
    sample_data = {}

    try:
        # Look for input fields specific to this endpoint first
        endpoint_selector = f"#{endpoint_id}"
        input_fields = driver.find_elements(By.CSS_SELECTOR, f"{endpoint_selector} input, {endpoint_selector} textarea, {endpoint_selector} select")

        if not input_fields:
            # Fallback to visible input fields
            all_fields = driver.find_elements(By.CSS_SELECTOR, "input, textarea, select")
            input_fields = [field for field in all_fields if field.is_displayed()]

        for field in input_fields:
            field_name = field.get_attribute('name') or field.get_attribute('id') or field.get_attribute('placeholder')
            field_type = field.get_attribute('type') or 'text'

            if field_name and field_name not in ['responseContentType']:  # Skip response type selectors
                # Generate sample data based on field name and type
                sample_value = generate_sample_value(field_name, field_type)

                if sample_value:
                    try:
                        # Clear and fill the field
                        field.clear()
                        field.send_keys(sample_value)
                        sample_data[field_name] = sample_value
                    except:
                        pass  # Skip if field is not editable

        # Also look for any pre-filled example data
        example_elements = driver.find_elements(By.CSS_SELECTOR, f"{endpoint_selector} .example-value, {endpoint_selector} .sample-data, {endpoint_selector} .default-value")
        for element in example_elements:
            if element.text.strip():
                sample_data['example_data'] = element.text.strip()

    except Exception as e:
        print(f"Error getting sample input data for {endpoint_name}: {e}")

    return sample_data

def generate_sample_value(field_name, field_type):
    """Generate appropriate sample values based on field name and type"""
    field_name_lower = field_name.lower()

    # Common API authentication fields
    if 'key' in field_name_lower or 'token' in field_name_lower:
        return "3ff0c443f405cae2ef723ccece2f8008e6a7a70a"
    elif 'secret' in field_name_lower:
        return "27dd3a9574a6c72be5986aea31554921d687bcef"
    elif 'phone' in field_name_lower:
        return "+441234567890"
    elif 'email' in field_name_lower:
        return "<EMAIL>"
    elif 'name' in field_name_lower:
        return "John Doe"
    elif 'address' in field_name_lower:
        return "123 Main Street, London, UK"
    elif 'postcode' in field_name_lower or 'zip' in field_name_lower:
        return "SW1A 1AA"
    elif 'date' in field_name_lower:
        return "2024-01-15"
    elif 'time' in field_name_lower:
        return "14:30"
    elif field_type == 'number':
        return "1"
    elif field_type == 'email':
        return "<EMAIL>"
    elif field_type == 'tel':
        return "+441234567890"
    else:
        return "test"

def capture_modal_response(driver, endpoint_id):
    """Capture response from modal popup"""
    modal_response = None

    try:
        # Wait for modal to appear
        time.sleep(2)

        # Look for endpoint-specific modal first
        endpoint_modal_selector = f"#modal-{endpoint_id}"
        modal_elements = driver.find_elements(By.CSS_SELECTOR, endpoint_modal_selector)

        if not modal_elements:
            # Fallback to general modal selectors
            modal_selectors = [
                ".modal-content", ".modal-body", ".response-modal",
                ".popup-content", ".dialog-content", "#response-modal"
            ]

            for selector in modal_selectors:
                try:
                    modal_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if modal_elements:
                        # Filter to visible modals
                        modal_elements = [modal for modal in modal_elements if modal.is_displayed()]
                        if modal_elements:
                            break
                except:
                    continue

        if modal_elements:
            modal = modal_elements[0]

            # Wait for content to load
            time.sleep(2)

            # Extract response text
            response_text = modal.text.strip()

            # Look for specific response elements
            response_elements = modal.find_elements(By.CSS_SELECTOR, ".response_body, .block.response_body")
            if response_elements:
                response_text = response_elements[0].text.strip()

            # Also try to get any JSON or code content
            code_elements = modal.find_elements(By.CSS_SELECTOR, "pre, code, .json-response")
            if code_elements:
                code_text = code_elements[0].text.strip()
                if code_text and code_text not in response_text:
                    response_text += "\n\nCode/JSON Response:\n" + code_text

            modal_response = response_text

            # Close modal if there's a close button
            close_buttons = modal.find_elements(By.CSS_SELECTOR,
                ".close, .modal-close, button[aria-label='Close']")
            if close_buttons:
                driver.execute_script("arguments[0].click();", close_buttons[0])
                time.sleep(1)

    except Exception as e:
        print(f"Error capturing modal response: {e}")

    return modal_response

def extract_content_to_markdown_selenium(element):
    """Extract content from a Selenium WebElement and convert to markdown"""
    try:
        # Get the HTML content and parse with BeautifulSoup
        html_content = element.get_attribute('innerHTML')
        soup = BeautifulSoup(html_content, 'html.parser')
        return extract_content_to_markdown(soup)
    except:
        # Fallback to text content
        return element.text + "\n\n"

def extract_table_data_selenium(table_element):
    """Extract data from a Selenium table element"""
    table_data = []

    try:
        rows = table_element.find_elements(By.TAG_NAME, "tr")

        for row in rows:
            cells = row.find_elements(By.TAG_NAME, "td") or row.find_elements(By.TAG_NAME, "th")
            row_data = [cell.text.strip() for cell in cells]
            if row_data:
                table_data.append(row_data)

    except Exception as e:
        print(f"Error extracting table data: {e}")

    return table_data

def save_endpoint_doc(endpoint_doc, endpoint_name):
    """Save individual endpoint documentation to a file"""
    try:
        # Create safe filename
        safe_filename = "".join(c for c in endpoint_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_filename = safe_filename.replace(' ', '_')
        filename = f"api_endpoints/{safe_filename}.md"

        # Create markdown content
        content = f"# {endpoint_doc['name']}\n\n"
        content += endpoint_doc['markdown']

        if endpoint_doc['parameters']:
            content += "\n## Parameters\n\n"
            for param_row in endpoint_doc['parameters']:
                if len(param_row) >= 2:
                    content += f"- **{param_row[0]}**: {param_row[1]}\n"

        if endpoint_doc['responses']:
            content += "\n## Responses\n\n"
            for response in endpoint_doc['responses']:
                content += f"```\n{response}\n```\n\n"

        if endpoint_doc['examples']:
            content += "\n## Examples\n\n"
            for example in endpoint_doc['examples']:
                content += f"```\n{example}\n```\n\n"

        if 'try_response' in endpoint_doc and endpoint_doc['try_response']:
            content += "\n## Try Response\n\n"
            try_resp = endpoint_doc['try_response']

            if try_resp['sample_input']:
                content += "### Sample Input\n\n"
                content += f"```json\n{json.dumps(try_resp['sample_input'], indent=2)}\n```\n\n"

            content += "### Response\n\n"
            content += f"```\n{try_resp['response']}\n```\n\n"
            content += f"*Captured at: {try_resp['timestamp']}*\n\n"

        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"Saved endpoint documentation: {filename}")

    except Exception as e:
        print(f"Error saving endpoint documentation: {e}")

def extract_table_to_markdown(table):
    """Convert HTML table to markdown format"""
    md_table = ""

    # Extract headers
    headers = [th.get_text().strip() for th in table.select('th')]
    if not headers and table.select('tr'):
        # If no th elements, use first row as header
        headers = [td.get_text().strip() for td in table.select('tr')[0].select('td')]

    if headers:
        md_table += "| " + " | ".join(headers) + " |\n"
        md_table += "| " + " | ".join(["---"] * len(headers)) + " |\n"

        # Extract rows (skip header row if we used it as headers)
        start_idx = 1 if not table.select('th') and table.select('tr') else 0
        for row in table.select('tr')[start_idx:]:
            cells = [td.get_text().strip() for td in row.select('td')]
            if cells:
                md_table += "| " + " | ".join(cells) + " |\n"

    return md_table

def extract_content_to_markdown(element):
    """Extract content from an HTML element and convert to markdown"""
    md_content = ""

    for child in element.children:
        if child.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            level = int(child.name[1])
            md_content += f"{'#' * level} {child.get_text().strip()}\n\n"
        elif child.name == 'p':
            md_content += f"{child.get_text().strip()}\n\n"
        elif child.name == 'ul':
            for li in child.select('li'):
                md_content += f"- {li.get_text().strip()}\n"
            md_content += "\n"
        elif child.name == 'ol':
            for i, li in enumerate(child.select('li'), 1):
                md_content += f"{i}. {li.get_text().strip()}\n"
            md_content += "\n"
        elif child.name == 'table':
            md_content += extract_table_to_markdown(child) + "\n"
        elif child.name == 'pre' or child.name == 'code':
            md_content += f"```\n{child.get_text().strip()}\n```\n\n"
        elif child.name == 'div' and len(list(child.children)) > 0:
            md_content += extract_content_to_markdown(child)

    return md_content

def save_to_file(content, filename):
    """Save content to a file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Documentation saved to {filename}")

if __name__ == "__main__":
    base_url = "https://api.icabbidispatch.com/docs/index.html"
    output_file = "icabbi_api_selenium.md"

    print("Starting enhanced API documentation scraping with Selenium...")
    print("This will:")
    print("1. Find all API endpoints from resources_nav")
    print("2. Extract content from .content elements")
    print("3. Execute Try buttons and capture modal responses")
    print("4. Generate individual documentation files for each endpoint")
    print("5. Create a comprehensive main documentation file")
    print()

    # Use Selenium-based scraping
    md_content = scrape_api_docs_with_selenium(base_url)

    if md_content:
        save_to_file(md_content, output_file)
        print(f"\nMain documentation saved to: {output_file}")
        print("Individual endpoint documentation saved to: api_endpoints/")
        print("\nScraping completed successfully!")
    else:
        print("Failed to generate documentation")
        print("Please ensure:")
        print("1. ChromeDriver is installed and in your PATH")
        print("2. Chrome browser is installed")
        print("3. Internet connection is available")