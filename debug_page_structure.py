#!/usr/bin/env python3
"""
Debug script to analyze the iCabbi API documentation page structure
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    # Remove headless mode for debugging
    # chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def analyze_page_structure():
    """Analyze the page structure to understand the DOM"""
    driver = setup_driver()
    
    try:
        print("Loading iCabbi API documentation...")
        driver.get("https://api.icabbidispatch.com/docs/index.html")
        
        # Wait for page to load
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        print(f"Current URL: {driver.current_url}")
        
        # Check for resources_nav
        print("\n=== Checking for resources_nav ===")
        try:
            resources_nav = driver.find_element(By.ID, "resources_nav")
            print(f"✓ Found resources_nav element")
            print(f"  Tag: {resources_nav.tag_name}")
            print(f"  Text content: {resources_nav.text[:200]}...")
            
            # Check for links within resources_nav
            links = resources_nav.find_elements(By.TAG_NAME, "a")
            print(f"  Found {len(links)} links in resources_nav")
            
            for i, link in enumerate(links[:5]):  # Show first 5 links
                href = link.get_attribute('href')
                text = link.text.strip()
                print(f"    Link {i+1}: '{text}' -> {href}")
                
        except Exception as e:
            print(f"✗ resources_nav not found: {e}")
        
        # Check for alternative navigation elements
        print("\n=== Checking for alternative navigation ===")
        nav_selectors = [
            "#navigation", ".navigation", ".nav", ".menu",
            ".sidebar", ".api-nav", ".docs-nav", "[role='navigation']"
        ]
        
        for selector in nav_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"✓ Found {len(elements)} elements with selector: {selector}")
                    for i, elem in enumerate(elements[:2]):
                        print(f"  Element {i+1}: {elem.tag_name}, text: {elem.text[:100]}...")
            except:
                pass
        
        # Check for content elements
        print("\n=== Checking for content elements ===")
        content_elements = driver.find_elements(By.CSS_SELECTOR, ".content")
        print(f"Found {len(content_elements)} .content elements")
        
        for i, content in enumerate(content_elements[:3]):
            print(f"  Content {i+1}: {content.text[:150]}...")
        
        # Check for Try buttons
        print("\n=== Checking for Try buttons ===")
        try_selectors = [
            "button[class*='try']", ".try-button", "input[value*='Try']",
            "//button[contains(text(), 'Try')]"
        ]
        
        for selector in try_selectors:
            try:
                if selector.startswith("//"):
                    buttons = driver.find_elements(By.XPATH, selector)
                else:
                    buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if buttons:
                    print(f"✓ Found {len(buttons)} Try buttons with selector: {selector}")
                    for i, btn in enumerate(buttons[:3]):
                        print(f"  Button {i+1}: {btn.text}, visible: {btn.is_displayed()}")
            except:
                pass
        
        # Check for modal elements
        print("\n=== Checking for modal elements ===")
        modal_selectors = [
            ".modal-content", ".modal-body", ".response-modal",
            ".popup-content", ".dialog-content", "#response-modal"
        ]
        
        for selector in modal_selectors:
            try:
                modals = driver.find_elements(By.CSS_SELECTOR, selector)
                if modals:
                    print(f"✓ Found {len(modals)} modal elements with selector: {selector}")
            except:
                pass
        
        # Get page source for manual inspection
        print("\n=== Saving page source ===")
        with open("page_source_debug.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Page source saved to: page_source_debug.html")
        
        # Take a screenshot
        print("\n=== Taking screenshot ===")
        driver.save_screenshot("page_screenshot_debug.png")
        print("Screenshot saved to: page_screenshot_debug.png")
        
        # Wait for user to inspect
        print("\n=== Manual inspection ===")
        print("Browser window is open. You can manually inspect the page.")
        print("Press Enter to continue...")
        input()
        
    except Exception as e:
        print(f"Error during analysis: {e}")
    
    finally:
        driver.quit()

if __name__ == "__main__":
    analyze_page_structure()
