#!/usr/bin/env python3
"""
Setup script for the iCabbi API scraper
This script will install the required dependencies and set up ChromeDriver
"""

import subprocess
import sys
import os

def install_requirements():
    """Install Python requirements"""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_scraper.txt"])
        print("✓ Python requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing requirements: {e}")
        return False

def install_chromedriver():
    """Install ChromeDriver using webdriver-manager"""
    print("Setting up ChromeDriver...")
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # Download and setup ChromeDriver
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        driver_path = ChromeDriverManager().install()
        
        # Test the driver
        driver = webdriver.Chrome(driver_path, options=chrome_options)
        driver.quit()
        
        print("✓ ChromeDriver installed and tested successfully")
        return True
    except Exception as e:
        print(f"✗ Error setting up ChromeDriver: {e}")
        print("Please install Chrome browser and try again")
        return False

def main():
    print("iCabbi API Scraper Setup")
    print("=" * 30)
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please install requirements manually:")
        print("pip install -r requirements_scraper.txt")
        return False
    
    # Install ChromeDriver
    if not install_chromedriver():
        print("ChromeDriver setup failed. You may need to install it manually.")
        print("Visit: https://chromedriver.chromium.org/")
        return False
    
    print("\n✓ Setup completed successfully!")
    print("\nYou can now run the scraper:")
    print("python scrape_icabbi_api.py")
    
    return True

if __name__ == "__main__":
    main()
