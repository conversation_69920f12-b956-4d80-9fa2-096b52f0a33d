<?php

use App\Http\Controllers\Admin\BookingManagementController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DriverManagementController;
use App\Http\Controllers\Admin\PaymentManagementController;
use App\Http\Controllers\Admin\SeoManagementController;
use App\Http\Controllers\Admin\SettingManagementController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\VehicleManagementController;
use App\Http\Controllers\Web\BookingController;
use App\Http\Controllers\Web\DriverController;
use App\Http\Controllers\Web\LocationController;
use App\Http\Controllers\Web\PaymentController;
use App\Http\Controllers\Web\ReviewController;
use App\Http\Controllers\Web\SettingController;
use App\Http\Controllers\Web\UserController;
use App\Http\Controllers\Web\VehicleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', [BookingController::class, 'index'])->name('home');

// Booking flow (public)
Route::post('/get-quote', [BookingController::class, 'getQuote'])->name('booking.quote');
Route::get('/select-vehicle', [BookingController::class, 'selectVehicle'])->name('booking.select-vehicle');
Route::post('/book-now', [BookingController::class, 'store'])->name('booking.store');

// Static pages
Route::get('/about-us', function () {
    return view('pages.about');
})->name('about');

Route::get('/our-services', function () {
    return view('pages.services');
})->name('services');

Route::get('/contact-us', function () {
    return view('pages.contact');
})->name('contact');

Route::get('/frequently-asked-questions', function () {
    return view('pages.faqs');
})->name('faqs');

Route::get('/terms-and-conditions', function () {
    return view('pages.terms');
})->name('terms');

Route::get('/privacy-policy', function () {
    return view('pages.privacy');
})->name('privacy');

// Vehicles (public)
Route::get('/our-fleet', [VehicleController::class, 'index'])->name('vehicles.index');

// Contact form submission
Route::post('/contact', [SettingController::class, 'submitContact'])->name('contact.submit');

// Auth routes
Route::middleware(['auth'])->group(function () {
    // User Profile
    Route::get('/profile', [UserController::class, 'profile'])->name('profile');
    Route::put('/profile', [UserController::class, 'updateProfile'])->name('profile.update');
    Route::put('/password', [UserController::class, 'updatePassword'])->name('password.update');

    // Bookings
    Route::get('/bookings', [BookingController::class, 'index'])->name('bookings.index');
    Route::get('/bookings/create', [BookingController::class, 'create'])->name('bookings.create');
    Route::post('/bookings', [BookingController::class, 'store'])->name('bookings.store');
    Route::get('/bookings/{booking}', [BookingController::class, 'show'])->name('bookings.show');
    Route::get('/bookings/{booking}/edit', [BookingController::class, 'edit'])->name('bookings.edit');
    Route::put('/bookings/{booking}', [BookingController::class, 'update'])->name('bookings.update');
    Route::delete('/bookings/{booking}', [BookingController::class, 'destroy'])->name('bookings.destroy');
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::get('/bookings/{booking}/track', [BookingController::class, 'track'])->name('bookings.track');

    // Reviews
    Route::post('/reviews', [ReviewController::class, 'store'])->name('reviews.store');
    Route::put('/reviews/{review}', [ReviewController::class, 'update'])->name('reviews.update');
    Route::delete('/reviews/{review}', [ReviewController::class, 'destroy'])->name('reviews.destroy');

    // Locations
    Route::get('/locations', [LocationController::class, 'index'])->name('locations.index');
    Route::post('/locations', [LocationController::class, 'store'])->name('locations.store');
    Route::put('/locations/{location}', [LocationController::class, 'update'])->name('locations.update');
    Route::delete('/locations/{location}', [LocationController::class, 'destroy'])->name('locations.destroy');
    Route::put('/locations/{location}/favorite', [LocationController::class, 'toggleFavorite'])->name('locations.favorite');

    // Payment Methods
    Route::get('/payment-methods', [PaymentController::class, 'index'])->name('payment-methods.index');
    Route::post('/payment-methods', [PaymentController::class, 'store'])->name('payment-methods.store');
    Route::put('/payment-methods/{paymentMethod}', [PaymentController::class, 'update'])->name('payment-methods.update');
    Route::delete('/payment-methods/{paymentMethod}', [PaymentController::class, 'destroy'])->name('payment-methods.destroy');
    Route::put('/payment-methods/{paymentMethod}/default', [PaymentController::class, 'setDefault'])->name('payment-methods.default');

    // Driver Portal (for driver role)
    Route::middleware(['driver'])->prefix('driver')->group(function () {
        Route::get('/dashboard', [DriverController::class, 'dashboard'])->name('driver.dashboard');
        Route::get('/bookings', [DriverController::class, 'bookings'])->name('driver.bookings');
        Route::get('/bookings/{booking}', [DriverController::class, 'showBooking'])->name('driver.bookings.show');
        Route::put('/bookings/{booking}/status', [DriverController::class, 'updateBookingStatus'])->name('driver.bookings.status');
        Route::put('/profile', [DriverController::class, 'updateProfile'])->name('driver.profile.update');
        Route::put('/location', [DriverController::class, 'updateLocation'])->name('driver.location.update');
        Route::put('/availability', [DriverController::class, 'toggleAvailability'])->name('driver.availability');
    });

    // Admin Routes
    Route::middleware(['admin'])->prefix('admin')->group(function () {
        // Dashboard
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('admin.dashboard');

        // User Management
        Route::get('/users', [UserManagementController::class, 'index'])->name('admin.users.index');
        Route::get('/users/create', [UserManagementController::class, 'create'])->name('admin.users.create');
        Route::post('/users', [UserManagementController::class, 'store'])->name('admin.users.store');
        Route::get('/users/{user}/edit', [UserManagementController::class, 'edit'])->name('admin.users.edit');
        Route::put('/users/{user}', [UserManagementController::class, 'update'])->name('admin.users.update');
        Route::delete('/users/{user}', [UserManagementController::class, 'destroy'])->name('admin.users.destroy');

        // Driver Management
        Route::get('/drivers', [DriverManagementController::class, 'index'])->name('admin.drivers.index');
        Route::get('/drivers/create', [DriverManagementController::class, 'create'])->name('admin.drivers.create');
        Route::post('/drivers', [DriverManagementController::class, 'store'])->name('admin.drivers.store');
        Route::get('/drivers/{driver}/edit', [DriverManagementController::class, 'edit'])->name('admin.drivers.edit');
        Route::put('/drivers/{driver}', [DriverManagementController::class, 'update'])->name('admin.drivers.update');
        Route::delete('/drivers/{driver}', [DriverManagementController::class, 'destroy'])->name('admin.drivers.destroy');

        // Vehicle Management
        Route::get('/vehicles', [VehicleManagementController::class, 'index'])->name('admin.vehicles.index');
        Route::get('/vehicles/create', [VehicleManagementController::class, 'create'])->name('admin.vehicles.create');
        Route::post('/vehicles', [VehicleManagementController::class, 'store'])->name('admin.vehicles.store');
        Route::get('/vehicles/{vehicle}/edit', [VehicleManagementController::class, 'edit'])->name('admin.vehicles.edit');
        Route::put('/vehicles/{vehicle}', [VehicleManagementController::class, 'update'])->name('admin.vehicles.update');
        Route::delete('/vehicles/{vehicle}', [VehicleManagementController::class, 'destroy'])->name('admin.vehicles.destroy');

        // Booking Management
        Route::get('/bookings', [BookingManagementController::class, 'index'])->name('admin.bookings.index');
        Route::get('/bookings/create', [BookingManagementController::class, 'create'])->name('admin.bookings.create');
        Route::post('/bookings', [BookingManagementController::class, 'store'])->name('admin.bookings.store');
        Route::get('/bookings/{booking}/edit', [BookingManagementController::class, 'edit'])->name('admin.bookings.edit');
        Route::put('/bookings/{booking}', [BookingManagementController::class, 'update'])->name('admin.bookings.update');
        Route::delete('/bookings/{booking}', [BookingManagementController::class, 'destroy'])->name('admin.bookings.destroy');
        Route::put('/bookings/{booking}/assign', [BookingManagementController::class, 'assignDriver'])->name('admin.bookings.assign');

        // Payment Management
        Route::get('/payments', [PaymentManagementController::class, 'index'])->name('admin.payments.index');
        Route::get('/payments/{payment}', [PaymentManagementController::class, 'show'])->name('admin.payments.show');
        Route::put('/payments/{payment}/status', [PaymentManagementController::class, 'updateStatus'])->name('admin.payments.status');
        Route::post('/payments/{payment}/refund', [PaymentManagementController::class, 'refund'])->name('admin.payments.refund');

        // Settings Management
        Route::get('/settings', [SettingManagementController::class, 'index'])->name('admin.settings.index');
        Route::post('/settings', [SettingManagementController::class, 'update'])->name('admin.settings.update');

        // SEO Management
        Route::get('/seo', [SeoManagementController::class, 'index'])->name('admin.seo.index');
        Route::put('/seo', [SeoManagementController::class, 'update'])->name('admin.seo.update');
        Route::get('/seo/sitemap', [SeoManagementController::class, 'sitemap'])->name('admin.seo.sitemap');
        Route::post('/seo/sitemap/generate', [SeoManagementController::class, 'generateSitemap'])->name('admin.seo.sitemap.generate');
    });
});
