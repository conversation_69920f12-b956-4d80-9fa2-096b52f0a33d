<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\BookingController;
use App\Http\Controllers\Api\DriverController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\VehicleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/auth/reset-password', [AuthController::class, 'resetPassword']);

// Vehicles (public)
Route::get('/vehicles', [VehicleController::class, 'index']);
Route::get('/vehicles/{vehicle}', [VehicleController::class, 'show']);

// Fare estimate
Route::post('/fare-estimate', [BookingController::class, 'getFareEstimate']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
    
    // User
    Route::get('/user', [UserController::class, 'show']);
    Route::put('/user', [UserController::class, 'update']);
    Route::put('/user/password', [UserController::class, 'updatePassword']);
    
    // Bookings
    Route::get('/bookings', [BookingController::class, 'index']);
    Route::post('/bookings', [BookingController::class, 'store']);
    Route::get('/bookings/{booking}', [BookingController::class, 'show']);
    Route::put('/bookings/{booking}', [BookingController::class, 'update']);
    Route::delete('/bookings/{booking}', [BookingController::class, 'destroy']);
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel']);
    Route::get('/bookings/{booking}/track', [BookingController::class, 'track']);
    
    // Reviews
    Route::get('/reviews', [ReviewController::class, 'index']);
    Route::post('/reviews', [ReviewController::class, 'store']);
    Route::get('/reviews/{review}', [ReviewController::class, 'show']);
    Route::put('/reviews/{review}', [ReviewController::class, 'update']);
    Route::delete('/reviews/{review}', [ReviewController::class, 'destroy']);
    
    // Locations
    Route::get('/locations', [LocationController::class, 'index']);
    Route::post('/locations', [LocationController::class, 'store']);
    Route::get('/locations/{location}', [LocationController::class, 'show']);
    Route::put('/locations/{location}', [LocationController::class, 'update']);
    Route::delete('/locations/{location}', [LocationController::class, 'destroy']);
    Route::put('/locations/{location}/favorite', [LocationController::class, 'toggleFavorite']);
    
    // Payment Methods
    Route::get('/payment-methods', [PaymentController::class, 'index']);
    Route::post('/payment-methods', [PaymentController::class, 'store']);
    Route::get('/payment-methods/{paymentMethod}', [PaymentController::class, 'show']);
    Route::put('/payment-methods/{paymentMethod}', [PaymentController::class, 'update']);
    Route::delete('/payment-methods/{paymentMethod}', [PaymentController::class, 'destroy']);
    Route::put('/payment-methods/{paymentMethod}/default', [PaymentController::class, 'setDefault']);
    
    // Driver specific routes
    Route::middleware(['driver'])->prefix('driver')->group(function () {
        Route::get('/bookings', [DriverController::class, 'bookings']);
        Route::get('/bookings/{booking}', [DriverController::class, 'showBooking']);
        Route::put('/bookings/{booking}/status', [DriverController::class, 'updateBookingStatus']);
        Route::put('/profile', [DriverController::class, 'updateProfile']);
        Route::put('/location', [DriverController::class, 'updateLocation']);
        Route::put('/availability', [DriverController::class, 'toggleAvailability']);
    });
}); 