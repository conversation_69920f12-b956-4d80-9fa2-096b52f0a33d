<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('method_type'); // credit_card, paypal, etc.
            $table->string('provider')->nullable(); // visa, mastercard, etc.
            $table->string('account_number')->nullable();
            $table->string('last_four')->nullable();
            $table->string('expiry_date')->nullable();
            $table->boolean('is_default')->default(false);
            $table->string('holder_name')->nullable();
            $table->string('billing_address')->nullable();
            $table->string('payment_token')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};
