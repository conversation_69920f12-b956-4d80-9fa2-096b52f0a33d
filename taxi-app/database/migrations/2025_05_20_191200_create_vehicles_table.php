<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('type');
            $table->string('model');
            $table->string('registration_number')->unique();
            $table->integer('capacity');
            $table->boolean('child_seat_available')->default(false);
            $table->boolean('extra_luggage_available')->default(false);
            $table->boolean('wheelchair_accessible')->default(false);
            $table->string('color');
            $table->boolean('active')->default(true);
            $table->decimal('base_price', 8, 2);
            $table->decimal('price_per_mile', 8, 2);
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->foreignId('driver_id')->nullable()->constrained()->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
