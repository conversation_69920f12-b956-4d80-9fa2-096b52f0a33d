<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->foreignId('driver_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignId('vehicle_id')->nullable()->constrained()->nullOnDelete();
            $table->string('booking_number')->unique();
            $table->string('status')->default('pending'); // pending, confirmed, in_progress, completed, cancelled
            $table->dateTime('pickup_time');
            $table->string('pickup_address');
            $table->decimal('pickup_latitude', 10, 7);
            $table->decimal('pickup_longitude', 10, 7);
            $table->string('dropoff_address');
            $table->decimal('dropoff_latitude', 10, 7);
            $table->decimal('dropoff_longitude', 10, 7);
            $table->decimal('distance', 8, 2)->nullable(); // in miles
            $table->decimal('estimated_fare', 8, 2)->nullable();
            $table->decimal('actual_fare', 8, 2)->nullable();
            $table->boolean('child_seat_required')->default(false);
            $table->boolean('extra_luggage_required')->default(false);
            $table->boolean('wheelchair_required')->default(false);
            $table->integer('passengers')->default(1);
            $table->text('special_instructions')->nullable();
            $table->string('payment_status')->default('unpaid'); // unpaid, paid, refunded
            $table->foreignId('payment_method_id')->nullable()->constrained()->nullOnDelete();
            $table->dateTime('completed_at')->nullable();
            $table->dateTime('cancelled_at')->nullable();
            $table->string('cancellation_reason')->nullable();
            $table->boolean('is_rated')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
