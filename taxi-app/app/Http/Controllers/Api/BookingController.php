<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display a listing of the bookings.
     */
    public function index(Request $request)
    {
        $bookings = $request->user()->bookings()->with(['driver.user', 'vehicle'])->latest()->get();

        return response()->json([
            'bookings' => $bookings
        ]);
    }

    /**
     * Store a newly created booking.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:vehicles,id',
            'pickup_time' => 'required|date|after:now',
            'pickup_address' => 'required|string',
            'pickup_latitude' => 'required|numeric',
            'pickup_longitude' => 'required|numeric',
            'dropoff_address' => 'required|string',
            'dropoff_latitude' => 'required|numeric',
            'dropoff_longitude' => 'required|numeric',
            'distance' => 'required|numeric|min:0.1',
            'child_seat_required' => 'boolean',
            'extra_luggage_required' => 'boolean',
            'wheelchair_required' => 'boolean',
            'passengers' => 'required|integer|min:1',
            'special_instructions' => 'nullable|string',
            'payment_method_id' => 'required|exists:payment_methods,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vehicle = Vehicle::findOrFail($request->vehicle_id);
        
        // Calculate estimated fare
        $estimatedFare = $vehicle->calculateFare($request->distance);

        $booking = new Booking([
            'user_id' => $request->user()->id,
            'vehicle_id' => $request->vehicle_id,
            'booking_number' => Booking::generateBookingNumber(),
            'status' => 'pending',
            'pickup_time' => $request->pickup_time,
            'pickup_address' => $request->pickup_address,
            'pickup_latitude' => $request->pickup_latitude,
            'pickup_longitude' => $request->pickup_longitude,
            'dropoff_address' => $request->dropoff_address,
            'dropoff_latitude' => $request->dropoff_latitude,
            'dropoff_longitude' => $request->dropoff_longitude,
            'distance' => $request->distance,
            'estimated_fare' => $estimatedFare,
            'child_seat_required' => $request->child_seat_required ?? false,
            'extra_luggage_required' => $request->extra_luggage_required ?? false,
            'wheelchair_required' => $request->wheelchair_required ?? false,
            'passengers' => $request->passengers,
            'special_instructions' => $request->special_instructions,
            'payment_status' => 'unpaid',
            'payment_method_id' => $request->payment_method_id,
        ]);

        $booking->save();

        return response()->json([
            'message' => 'Booking created successfully',
            'booking' => $booking->load(['vehicle'])
        ], 201);
    }

    /**
     * Display the specified booking.
     */
    public function show(Request $request, Booking $booking)
    {
        // Check if the booking belongs to the authenticated user
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json([
            'booking' => $booking->load(['driver.user', 'vehicle', 'paymentMethod'])
        ]);
    }

    /**
     * Update the specified booking.
     */
    public function update(Request $request, Booking $booking)
    {
        // Check if the booking belongs to the authenticated user
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if the booking can be updated (only pending bookings can be updated)
        if ($booking->status !== 'pending') {
            return response()->json(['message' => 'Only pending bookings can be updated'], 422);
        }

        $validator = Validator::make($request->all(), [
            'pickup_time' => 'sometimes|required|date|after:now',
            'pickup_address' => 'sometimes|required|string',
            'pickup_latitude' => 'sometimes|required|numeric',
            'pickup_longitude' => 'sometimes|required|numeric',
            'dropoff_address' => 'sometimes|required|string',
            'dropoff_latitude' => 'sometimes|required|numeric',
            'dropoff_longitude' => 'sometimes|required|numeric',
            'child_seat_required' => 'sometimes|boolean',
            'extra_luggage_required' => 'sometimes|boolean',
            'wheelchair_required' => 'sometimes|boolean',
            'passengers' => 'sometimes|required|integer|min:1',
            'special_instructions' => 'nullable|string',
            'payment_method_id' => 'sometimes|required|exists:payment_methods,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $booking->update($request->all());

        // Recalculate estimated fare if distance or vehicle has changed
        if ($request->has('distance') || $request->has('vehicle_id')) {
            $vehicle = $request->has('vehicle_id') 
                ? Vehicle::findOrFail($request->vehicle_id) 
                : $booking->vehicle;
            
            $distance = $request->has('distance') ? $request->distance : $booking->distance;
            $booking->estimated_fare = $vehicle->calculateFare($distance);
            $booking->save();
        }

        return response()->json([
            'message' => 'Booking updated successfully',
            'booking' => $booking->fresh()->load(['driver.user', 'vehicle', 'paymentMethod'])
        ]);
    }

    /**
     * Cancel the specified booking.
     */
    public function cancel(Request $request, Booking $booking)
    {
        // Check if the booking belongs to the authenticated user
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if the booking can be canceled (pending or confirmed bookings can be canceled)
        if (!in_array($booking->status, ['pending', 'confirmed'])) {
            return response()->json(['message' => 'Only pending or confirmed bookings can be canceled'], 422);
        }

        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $booking->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $request->cancellation_reason,
        ]);

        return response()->json([
            'message' => 'Booking cancelled successfully',
            'booking' => $booking->fresh()
        ]);
    }

    /**
     * Remove the specified booking.
     */
    public function destroy(Request $request, Booking $booking)
    {
        // Check if the booking belongs to the authenticated user
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow deletion of cancelled bookings more than 30 days old
        if ($booking->status !== 'cancelled' || $booking->cancelled_at->diffInDays(now()) < 30) {
            return response()->json(['message' => 'Only cancelled bookings older than 30 days can be deleted'], 422);
        }

        $booking->delete();

        return response()->json(['message' => 'Booking deleted successfully']);
    }

    /**
     * Get a fare estimate.
     */
    public function getFareEstimate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:vehicles,id',
            'distance' => 'required|numeric|min:0.1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $vehicle = Vehicle::findOrFail($request->vehicle_id);
        
        $estimatedFare = $vehicle->calculateFare($request->distance);

        return response()->json([
            'estimated_fare' => $estimatedFare,
            'currency' => 'GBP',
            'distance' => $request->distance,
            'vehicle' => $vehicle
        ]);
    }

    /**
     * Track a booking.
     */
    public function track(Request $request, Booking $booking)
    {
        // Check if the booking belongs to the authenticated user
        if ($booking->user_id !== $request->user()->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Check if the booking is in progress and has a driver assigned
        if ($booking->status !== 'in_progress' || !$booking->driver_id) {
            return response()->json(['message' => 'Booking is not in progress or no driver assigned'], 422);
        }

        // Get the latest driver location
        $driver = $booking->driver;

        return response()->json([
            'booking' => $booking,
            'driver' => [
                'name' => $driver->user->name,
                'phone' => $driver->phone_number,
                'latitude' => $driver->latitude,
                'longitude' => $driver->longitude,
                'last_update' => $driver->last_location_update,
                'vehicle' => $booking->vehicle
            ],
            'estimated_arrival_time' => now()->addMinutes(15) // Placeholder, should be calculated based on distance
        ]);
    }
}
