<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\Booking;
use App\Models\Review;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    /**
     * Show the main landing page with quote form
     */
    public function index()
    {
        // Get active vehicles from database
        $vehicles = Vehicle::active()
            ->select('id', 'type', 'model', 'capacity', 'base_price', 'description', 'image')
            ->orderBy('base_price')
            ->get()
            ->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'name' => $vehicle->type . ' - ' . $vehicle->model,
                    'type' => $vehicle->type,
                    'description' => $vehicle->description,
                    'passengers' => $vehicle->capacity,
                    'price' => $vehicle->base_price,
                    'image' => $vehicle->image ?: 'default-car.svg',
                ];
            });

        // Get recent reviews
        $testimonials = Review::with('user')
            ->where('rating', '>=', 4)
            ->latest()
            ->limit(6)
            ->get()
            ->map(function ($review) {
                return [
                    'name' => $review->user->name,
                    'rating' => $review->rating,
                    'comment' => $review->comment,
                    'date' => $review->created_at->format('Y-m-d'),
                ];
            });

        $data = [
            'pageTitle' => 'Gatwick Taxi Cab - Premium Airport Transfers',
            'metaDescription' => 'Book reliable airport transfers with Gatwick Taxi Cab. Professional drivers, fixed prices, luxury vehicles. Serving Gatwick, Heathrow, and all London airports.',
            'services' => $this->getServices(),
            'vehicles' => $vehicles,
            'testimonials' => $testimonials->isNotEmpty() ? $testimonials : $this->getDefaultTestimonials(),
            'contactInfo' => $this->getContactInfo(),
            'stats' => $this->getCompanyStats(),
        ];

        return view('booking.index', $data);
    }

    /**
     * Get quote for the journey
     */
    public function getQuote(Request $request)
    {
        $request->validate([
            'pickup_location' => 'required|string|max:255',
            'dropoff_location' => 'required|string|max:255',
            'pickup_date' => 'required|date|after_or_equal:today',
            'pickup_time' => 'required',
            'passengers' => 'required|integer|min:1|max:16',
        ]);

        // Calculate distance (static for now, will be replaced with Google Maps API)
        $distance = $this->calculateStaticDistance($request->pickup_location, $request->dropoff_location);

        // Get available vehicles that can accommodate the passengers
        $availableVehicles = Vehicle::active()
            ->where('capacity', '>=', $request->passengers)
            ->orderBy('base_price')
            ->get()
            ->map(function ($vehicle) use ($distance) {
                $estimatedFare = $vehicle->calculateFare($distance);
                return [
                    'id' => $vehicle->id,
                    'name' => $vehicle->type . ' - ' . $vehicle->model,
                    'type' => $vehicle->type,
                    'description' => $vehicle->description,
                    'passengers' => $vehicle->capacity,
                    'base_price' => $vehicle->base_price,
                    'estimated_fare' => $estimatedFare,
                    'price_per_mile' => $vehicle->price_per_mile,
                    'image' => $vehicle->image ?: 'default-car.svg',
                    'features' => $this->getVehicleFeatures($vehicle),
                ];
            });

        // Store quote data in session for next step
        $quoteData = [
            'pickup_location' => $request->pickup_location,
            'dropoff_location' => $request->dropoff_location,
            'pickup_date' => $request->pickup_date,
            'pickup_time' => $request->pickup_time,
            'passengers' => $request->passengers,
            'distance' => $distance,
            'duration' => $this->calculateStaticDuration($distance),
        ];

        session(['quote' => $quoteData]);

        $data = [
            'pageTitle' => 'Select Your Vehicle - Gatwick Taxi Cab',
            'metaDescription' => 'Choose from our fleet of luxury vehicles for your airport transfer. Saloon cars, executive vehicles, and minibuses available.',
            'quote' => $quoteData,
            'vehicles' => $availableVehicles,
            'features' => $this->getServiceFeatures(),
        ];

        return view('booking.quote', $data);
    }

    /**
     * Show vehicle selection page
     */
    public function selectVehicle(Request $request)
    {
        $vehicleId = $request->get('vehicle');
        $quote = session('quote');

        if (!$quote) {
            return redirect()->route('home')->with('error', 'Please start by getting a quote.');
        }

        $vehicle = Vehicle::active()->find($vehicleId);

        if (!$vehicle) {
            return redirect()->route('home')->with('error', 'Invalid vehicle selection.');
        }

        // Calculate final fare
        $estimatedFare = $vehicle->calculateFare($quote['distance']);

        $vehicleData = [
            'id' => $vehicle->id,
            'name' => $vehicle->type . ' - ' . $vehicle->model,
            'type' => $vehicle->type,
            'description' => $vehicle->description,
            'passengers' => $vehicle->capacity,
            'estimated_fare' => $estimatedFare,
            'image' => $vehicle->image ?: 'default-car.svg',
            'features' => $this->getVehicleFeatures($vehicle),
        ];

        $data = [
            'pageTitle' => 'Book Your ' . $vehicleData['name'] . ' - Gatwick Taxi Cab',
            'metaDescription' => 'Complete your booking for ' . $vehicleData['name'] . '. Enter passenger details and payment information.',
            'quote' => $quote,
            'vehicle' => $vehicleData,
            'paymentMethods' => $this->getPaymentMethods(),
            'extras' => $this->getBookingExtras(),
        ];

        return view('booking.form', $data);
    }

    /**
     * Process booking and store in database
     */
    public function store(Request $request)
    {
        $request->validate([
            'passenger_name' => 'required|string|max:255',
            'passenger_email' => 'required|email|max:255',
            'passenger_phone' => 'required|string|max:20',
            'vehicle_id' => 'required|exists:vehicles,id',
            'payment_method' => 'required|string|in:card,cash',
            'special_requirements' => 'nullable|string|max:500',
            'child_seat_required' => 'boolean',
            'extra_luggage_required' => 'boolean',
            'wheelchair_required' => 'boolean',
        ]);

        $quote = session('quote');
        if (!$quote) {
            return redirect()->route('home')->with('error', 'Session expired. Please start over.');
        }

        $vehicle = Vehicle::findOrFail($request->vehicle_id);

        // Generate unique booking number
        $bookingNumber = 'GTC' . date('Ymd') . strtoupper(Str::random(4));

        // Calculate final fare
        $estimatedFare = $vehicle->calculateFare($quote['distance']);

        // Create pickup datetime
        $pickupDateTime = $quote['pickup_date'] . ' ' . $quote['pickup_time'];

        try {
            DB::beginTransaction();

            // Create booking record
            $booking = Booking::create([
                'user_id' => null, // Guest booking for now
                'vehicle_id' => $vehicle->id,
                'booking_number' => $bookingNumber,
                'status' => 'pending',
                'pickup_time' => $pickupDateTime,
                'pickup_address' => $quote['pickup_location'],
                'pickup_latitude' => 0, // Will be updated with geocoding
                'pickup_longitude' => 0,
                'dropoff_address' => $quote['dropoff_location'],
                'dropoff_latitude' => 0,
                'dropoff_longitude' => 0,
                'distance' => $quote['distance'],
                'estimated_fare' => $estimatedFare,
                'passengers' => $quote['passengers'],
                'child_seat_required' => $request->boolean('child_seat_required'),
                'extra_luggage_required' => $request->boolean('extra_luggage_required'),
                'wheelchair_required' => $request->boolean('wheelchair_required'),
                'special_instructions' => $request->special_requirements,
                'payment_status' => 'pending',
            ]);

            // Store guest information in session for confirmation page
            session([
                'guest_booking' => [
                    'name' => $request->passenger_name,
                    'email' => $request->passenger_email,
                    'phone' => $request->passenger_phone,
                    'payment_method' => $request->payment_method,
                ]
            ]);

            DB::commit();

            // Clear quote session
            session()->forget('quote');

            $data = [
                'pageTitle' => 'Booking Confirmed - Gatwick Taxi Cab',
                'metaDescription' => 'Your booking has been confirmed. Reference: ' . $bookingNumber,
                'booking' => $booking,
                'vehicle' => $vehicle,
                'guest' => session('guest_booking'),
                'contactInfo' => $this->getContactInfo(),
                'faqs' => $this->getBookingFaqs(),
            ];

            return view('booking.confirmation', $data);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with('error', 'There was an error processing your booking. Please try again.');
        }
    }

    /**
     * Get static services data
     */
    private function getServices()
    {
        return [
            [
                'title' => 'Airport Transfers',
                'description' => 'Reliable airport pickup and drop-off to all major UK airports, including Heathrow, Gatwick, and London City.',
                'icon' => 'airport',
                'features' => ['Flight monitoring', 'Meet & greet service', 'Fixed pricing']
            ],
            [
                'title' => 'Corporate Travel',
                'description' => 'Professional transportation for business meetings, events, and conferences with luxury vehicles.',
                'icon' => 'business',
                'features' => ['Executive vehicles', 'Professional drivers', 'Flexible scheduling']
            ],
            [
                'title' => 'Long Distance Journeys',
                'description' => 'Comfortable and reliable transportation for longer distances throughout the UK.',
                'icon' => 'distance',
                'features' => ['Comfortable seating', 'Rest stops', 'Competitive rates']
            ],
        ];
    }



    /**
     * Get default testimonials (fallback when no reviews in database)
     */
    private function getDefaultTestimonials()
    {
        return collect([
            [
                'name' => 'Sarah Johnson',
                'rating' => 5,
                'comment' => 'Excellent service! Driver was punctual, professional, and the car was immaculate. Will definitely use Gatwick Taxi Cab again for my airport transfers.',
                'date' => '2024-01-15'
            ],
            [
                'name' => 'Michael Roberts',
                'rating' => 5,
                'comment' => 'I use Gatwick Taxi Cab regularly for business trips. Their executive service is top-notch – reliable, comfortable, and always on time. Highly recommended!',
                'date' => '2024-01-10'
            ],
            [
                'name' => 'Emma Williams',
                'rating' => 5,
                'comment' => 'We booked an MPV for our family trip. The vehicle was spacious and comfortable. The driver helped with our luggage and was very friendly. Great experience!',
                'date' => '2024-01-08'
            ],
        ]);
    }

    /**
     * Get company statistics
     */
    private function getCompanyStats()
    {
        return [
            'total_bookings' => Booking::count(),
            'happy_customers' => Booking::where('status', 'completed')->count(),
            'years_experience' => 15,
            'fleet_size' => Vehicle::active()->count(),
        ];
    }

    /**
     * Get vehicle-specific features
     */
    private function getVehicleFeatures(Vehicle $vehicle)
    {
        $features = ['Professional driver', 'Air conditioning', 'Free WiFi'];

        if ($vehicle->child_seat_available) {
            $features[] = 'Child seats available';
        }

        if ($vehicle->extra_luggage_available) {
            $features[] = 'Extra luggage space';
        }

        if ($vehicle->wheelchair_accessible) {
            $features[] = 'Wheelchair accessible';
        }

        // Add type-specific features
        switch (strtolower($vehicle->type)) {
            case 'executive':
                $features[] = 'Leather seats';
                $features[] = 'Premium sound system';
                break;
            case 'luxury':
                $features[] = 'Luxury interior';
                $features[] = 'Entertainment system';
                break;
            case 'minibus':
                $features[] = 'Group travel';
                $features[] = 'Ample luggage space';
                break;
        }

        return $features;
    }

    /**
     * Get contact information
     */
    private function getContactInfo()
    {
        return [
            'phone' => '+44 20 1234 5678',
            'email' => '<EMAIL>',
            'address' => '123 Airport Road, London, UK',
            'hours' => '24/7 Service Available',
            'social' => [
                'facebook' => '#',
                'instagram' => '#',
                'twitter' => '#',
            ]
        ];
    }



    /**
     * Calculate static distance (will be replaced with API)
     */
    private function calculateStaticDistance($pickup, $dropoff)
    {
        // Static calculation - in real implementation, use Google Maps API
        $distances = [
            'gatwick' => 25,
            'heathrow' => 35,
            'london city' => 15,
            'stansted' => 45,
            'luton' => 40,
        ];

        $pickup_lower = strtolower($pickup);
        $dropoff_lower = strtolower($dropoff);

        foreach ($distances as $location => $distance) {
            if (strpos($pickup_lower, $location) !== false || strpos($dropoff_lower, $location) !== false) {
                return $distance;
            }
        }

        return 30; // Default distance
    }

    /**
     * Calculate static duration
     */
    private function calculateStaticDuration($distance)
    {
        return round($distance * 1.5); // Rough calculation: 1.5 minutes per mile
    }



    /**
     * Get service features
     */
    private function getServiceFeatures()
    {
        return [
            'Free Cancellation up to 2 hours before pickup',
            '24/7 Customer Support',
            'Professional Licensed Drivers',
            'Flight Monitoring for Airport Pickups',
            'Meet & Greet Service',
            'Fixed Pricing - No Hidden Fees',
        ];
    }

    /**
     * Get payment methods
     */
    private function getPaymentMethods()
    {
        return [
            [
                'id' => 'card',
                'name' => 'Pay by Card',
                'description' => 'Secure online payment with Visa, Mastercard, or American Express',
                'icon' => 'credit-card',
                'accepted_cards' => ['visa', 'mastercard', 'amex', 'apple-pay', 'google-pay']
            ],
            [
                'id' => 'cash',
                'name' => 'Pay by Cash',
                'description' => 'Pay the driver directly in cash upon arrival',
                'icon' => 'cash',
                'note' => 'Please have exact change ready'
            ],
        ];
    }

    /**
     * Get booking extras
     */
    private function getBookingExtras()
    {
        return [
            [
                'id' => 'child_seat',
                'name' => 'Child Seat',
                'price' => 5,
                'description' => 'Safety child seat for children under 12'
            ],
            [
                'id' => 'booster_seat',
                'name' => 'Booster Seat',
                'price' => 3,
                'description' => 'Booster seat for children 4-12 years'
            ],
            [
                'id' => 'meet_greet',
                'name' => 'Meet & Greet',
                'price' => 10,
                'description' => 'Driver will meet you at arrivals with name board'
            ],
            [
                'id' => 'wait_time',
                'name' => 'Extended Wait Time',
                'price' => 15,
                'description' => 'Additional 30 minutes wait time included'
            ],
        ];
    }

    /**
     * Get booking FAQs
     */
    private function getBookingFaqs()
    {
        return [
            [
                'question' => 'How do I modify or cancel my booking?',
                'answer' => 'You can modify or cancel your booking up to 2 hours before your scheduled pickup time by calling our customer service team.'
            ],
            [
                'question' => 'What if my flight is delayed?',
                'answer' => 'We monitor all flights automatically. If your flight is delayed, we will adjust your pickup time accordingly at no extra charge.'
            ],
            [
                'question' => 'Do you provide child seats?',
                'answer' => 'Yes, we provide child seats and booster seats for an additional fee. Please specify your requirements when booking.'
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept all major credit cards (Visa, Mastercard, American Express) and cash payments to the driver.'
            ],
            [
                'question' => 'Is there a cancellation fee?',
                'answer' => 'Cancellations made more than 2 hours before pickup are free. Cancellations within 2 hours may incur a fee.'
            ],
        ];
    }
}