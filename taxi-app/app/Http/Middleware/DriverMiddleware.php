<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DriverMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user() || !$request->user()->isDriver()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthorized. Driver access required.'], 403);
            }
            return redirect()->route('home')->with('error', 'You do not have permission to access this page. Driver access required.');
        }

        return $next($request);
    }
}
