<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vehicle extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'type',
        'model',
        'registration_number',
        'capacity',
        'child_seat_available',
        'extra_luggage_available',
        'wheelchair_accessible',
        'color',
        'active',
        'base_price',
        'price_per_mile',
        'description',
        'image',
        'driver_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'capacity' => 'integer',
        'child_seat_available' => 'boolean',
        'extra_luggage_available' => 'boolean',
        'wheelchair_accessible' => 'boolean',
        'active' => 'boolean',
        'base_price' => 'decimal:2',
        'price_per_mile' => 'decimal:2',
    ];

    /**
     * Get the driver that owns the vehicle.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Get the bookings for the vehicle.
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Scope a query to only include active vehicles.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Calculate the estimated fare for a given distance.
     */
    public function calculateFare(float $distance): float
    {
        return $this->base_price + ($this->price_per_mile * $distance);
    }
}
