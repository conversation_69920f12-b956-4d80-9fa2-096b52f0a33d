/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./resources/**/*.blade.php",
    "./resources/**/*.js",
    "./resources/**/*.vue",
  ],
  safelist: [
    // Primary colors
    'bg-primary-50', 'bg-primary-100', 'bg-primary-200', 'bg-primary-300', 'bg-primary-400', 'bg-primary-500', 'bg-primary-600', 'bg-primary-700', 'bg-primary-800', 'bg-primary-900',
    'text-primary-50', 'text-primary-100', 'text-primary-200', 'text-primary-300', 'text-primary-400', 'text-primary-500', 'text-primary-600', 'text-primary-700', 'text-primary-800', 'text-primary-900',
    'border-primary-50', 'border-primary-100', 'border-primary-200', 'border-primary-300', 'border-primary-400', 'border-primary-500', 'border-primary-600', 'border-primary-700', 'border-primary-800', 'border-primary-900',
    'hover:bg-primary-50', 'hover:bg-primary-100', 'hover:bg-primary-200', 'hover:bg-primary-300', 'hover:bg-primary-400', 'hover:bg-primary-500', 'hover:bg-primary-600', 'hover:bg-primary-700', 'hover:bg-primary-800', 'hover:bg-primary-900',
    'hover:text-primary-50', 'hover:text-primary-100', 'hover:text-primary-200', 'hover:text-primary-300', 'hover:text-primary-400', 'hover:text-primary-500', 'hover:text-primary-600', 'hover:text-primary-700', 'hover:text-primary-800', 'hover:text-primary-900',
    'hover:border-primary-50', 'hover:border-primary-100', 'hover:border-primary-200', 'hover:border-primary-300', 'hover:border-primary-400', 'hover:border-primary-500', 'hover:border-primary-600', 'hover:border-primary-700', 'hover:border-primary-800', 'hover:border-primary-900',
    'focus:border-primary-500',
    // Accent colors
    'bg-accent-gold', 'bg-accent-gold-light', 'bg-accent-gold-dark',
    'text-accent-gold', 'text-accent-gold-light', 'text-accent-gold-dark',
    'border-accent-gold', 'border-accent-gold-light', 'border-accent-gold-dark',
    'hover:bg-accent-gold', 'hover:bg-accent-gold-light', 'hover:bg-accent-gold-dark',
    'hover:text-accent-gold', 'hover:text-accent-gold-light', 'hover:text-accent-gold-dark',
    'hover:border-accent-gold', 'hover:border-accent-gold-light', 'hover:border-accent-gold-dark',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#E8F2FF',
          100: '#D1E5FF',
          200: '#A3CBFF',
          300: '#75B1FF',
          400: '#4797FF',
          500: '#124E81', // Primary Blue
          600: '#0E3E66',
          700: '#0A2E4B',
          800: '#061E30',
          900: '#020E15',
        },
        secondary: {
          DEFAULT: '#FFFFFF',
          light: '#F8F9FA',
          dark: '#E9ECEF',
        },
        accent: {
          gold: '#D4AF37',
          'gold-light': '#E3C533',
          'gold-dark': '#B28E1D',
        },
        gray: {
          50: '#F8F9FA',
          100: '#E9ECEF',
          200: '#DEE2E6',
          300: '#CED4DA',
          400: '#ADB5BD',
          500: '#6C757D',
          600: '#495057',
          700: '#343A40',
          800: '#212529',
          900: '#000000',
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
    },
  },
  plugins: [],
}