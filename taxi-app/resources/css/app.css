@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root {
  /* Primary Brand Colors */
  --color-primary-50: #E8F2FF;
  --color-primary-100: #D1E5FF;
  --color-primary-200: #A3CBFF;
  --color-primary-300: #75B1FF;
  --color-primary-400: #4797FF;
  --color-primary-500: #124E81; /* Primary Blue */
  --color-primary-600: #0E3E66;
  --color-primary-700: #0A2E4B;
  --color-primary-800: #061E30;
  --color-primary-900: #020E15;

  /* Secondary Colors */
  --color-secondary: #FFFFFF; /* White */
  --color-secondary-light: #F8F9FA;
  --color-secondary-dark: #E9ECEF;

  /* Accent Colors */
  --color-accent-gold: #D4AF37;
  --color-accent-gold-light: #E3C533;
  --color-accent-gold-dark: #B28E1D;

  /* Neutral Colors */
  --color-gray-50: #F8F9FA;
  --color-gray-100: #E9ECEF;
  --color-gray-200: #DEE2E6;
  --color-gray-300: #CED4DA;
  --color-gray-400: #ADB5BD;
  --color-gray-500: #6C757D;
  --color-gray-600: #495057;
  --color-gray-700: #343A40;
  --color-gray-800: #212529;
  --color-gray-900: #000000;
}

html {
  scroll-behavior: smooth;
}

/* Custom component classes */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-primary {
  background-color: var(--color-primary-500);
  color: var(--color-secondary);
  border-color: var(--color-primary-500);
}
.btn-primary:hover {
  background-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(18, 78, 129, 0.3);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary-500);
  border-color: var(--color-primary-500);
}
.btn-secondary:hover {
  background-color: var(--color-primary-500);
  color: var(--color-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(18, 78, 129, 0.2);
}

.btn-accent {
  background-color: var(--color-accent-gold);
  color: var(--color-gray-900);
  border-color: var(--color-accent-gold);
}
.btn-accent:hover {
  background-color: var(--color-accent-gold-dark);
  border-color: var(--color-accent-gold-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.btn-white {
  background-color: var(--color-secondary);
  color: var(--color-primary-500);
  border-color: var(--color-secondary);
}
.btn-white:hover {
  background-color: var(--color-secondary-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

.container-custom {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.section {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .section {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}
