#!/usr/bin/env python3
"""
Test script for the iCabbi API scraper
This script will test the basic functionality without running the full scraper
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import requests
        print("✓ requests")
    except ImportError:
        print("✗ requests - run: pip install requests")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ beautifulsoup4")
    except ImportError:
        print("✗ beautifulsoup4 - run: pip install beautifulsoup4")
        return False
    
    try:
        from selenium import webdriver
        print("✓ selenium")
    except ImportError:
        print("✗ selenium - run: pip install selenium")
        return False
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ webdriver-manager")
    except ImportError:
        print("✗ webdriver-manager - run: pip install webdriver-manager")
        return False
    
    return True

def test_chrome_driver():
    """Test if ChromeDriver can be set up"""
    print("\nTesting ChromeDriver setup...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✓ ChromeDriver working - tested with Google (title: {title[:50]}...)")
        return True
        
    except Exception as e:
        print(f"✗ ChromeDriver setup failed: {e}")
        return False

def test_api_access():
    """Test if the API documentation page is accessible"""
    print("\nTesting API access...")
    
    try:
        import requests
        
        url = "https://api.icabbidispatch.com/docs/index.html"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        print(f"✓ API documentation accessible (status: {response.status_code})")
        print(f"  Content length: {len(response.text)} characters")
        
        # Check if it contains expected content
        if "iCabbi" in response.text:
            print("✓ Content appears to be iCabbi documentation")
        else:
            print("⚠ Content may not be the expected iCabbi documentation")
        
        return True
        
    except Exception as e:
        print(f"✗ API access failed: {e}")
        return False

def main():
    print("iCabbi API Scraper Test")
    print("=" * 30)
    
    all_tests_passed = True
    
    # Test imports
    if not test_imports():
        all_tests_passed = False
    
    # Test ChromeDriver
    if not test_chrome_driver():
        all_tests_passed = False
    
    # Test API access
    if not test_api_access():
        all_tests_passed = False
    
    print("\n" + "=" * 30)
    if all_tests_passed:
        print("✓ All tests passed! The scraper should work correctly.")
        print("\nYou can now run the full scraper:")
        print("python scrape_icabbi_api.py")
    else:
        print("✗ Some tests failed. Please fix the issues above.")
        print("\nTo install missing dependencies:")
        print("pip install -r requirements_scraper.txt")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
