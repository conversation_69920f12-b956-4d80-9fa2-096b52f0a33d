# iCabbi API Documentation Scraper

This enhanced Python script scrapes the iCabbi API documentation, including interactive elements like "Try" buttons and modal responses. It creates comprehensive documentation for each API endpoint with sample inputs and responses.

## Features

- **Selenium-based scraping** - Handles JavaScript-rendered content and interactive elements
- **Resources navigation detection** - Finds API endpoints from the `resources_nav` DOM element
- **Content extraction** - Extracts operation requirements and parameters from `.content` elements
- **Try button execution** - Automatically clicks "Try" buttons and captures responses from modal popups
- **Sample data generation** - Generates appropriate sample input data for API testing
- **Individual endpoint docs** - Creates separate documentation files for each API endpoint
- **Comprehensive main documentation** - Combines all endpoints into a single comprehensive file

## Requirements

- Python 3.7+
- Chrome browser
- Internet connection

## Installation

### Option 1: Automatic Setup (Recommended)

```bash
# Run the setup script
python setup_scraper.py
```

### Option 2: Manual Installation

```bash
# Install Python dependencies
pip install -r requirements_scraper.txt

# The script will automatically download ChromeDriver when needed
```

### Option 3: Individual Package Installation

```bash
pip install selenium>=4.0.0
pip install beautifulsoup4>=4.9.0
pip install requests>=2.25.0
pip install markdown>=3.3.0
pip install webdriver-manager>=3.8.0
```

## Usage

### 1. Test the Setup

```bash
python test_scraper.py
```

This will verify that all dependencies are installed and working correctly.

### 2. Run the Scraper

```bash
python scrape_icabbi_api.py
```

### 3. Output Files

The scraper will create:

- `icabbi_api_selenium.md` - Main comprehensive documentation
- `api_endpoints/` directory - Individual endpoint documentation files
- Each endpoint file contains:
  - Endpoint description and parameters
  - Sample input data
  - Try button response (if available)
  - Code examples

## How It Works

1. **Navigation Discovery**: Finds the `resources_nav` element containing all API endpoints
2. **Content Extraction**: For each endpoint, extracts content from `.content` elements
3. **Parameter Analysis**: Identifies and documents required parameters and their types
4. **Interactive Testing**: 
   - Fills input fields with appropriate sample data
   - Clicks "Try" buttons
   - Captures responses from `.modal-content` popups
5. **Documentation Generation**: Creates markdown files with all extracted information

## Sample Data Generation

The scraper automatically generates appropriate sample data based on field names:

- `key`/`token` fields → Uses provided API key
- `secret` fields → Uses provided secret key
- `phone` fields → `+441234567890`
- `email` fields → `<EMAIL>`
- `name` fields → `John Doe`
- `address` fields → `123 Main Street, London, UK`
- `postcode`/`zip` fields → `SW1A 1AA`
- `date` fields → `2024-01-15`
- `time` fields → `14:30`

## Configuration

The script uses these API credentials (as provided):

- **API URL**: `https://api.icabbidispatch.com/icd/`
- **APP KEY**: `3ff0c443f405cae2ef723ccece2f8008e6a7a70a`
- **SECRET KEY**: `27dd3a9574a6c72be5986aea31554921d687bcef`

## Troubleshooting

### ChromeDriver Issues

If you encounter ChromeDriver issues:

1. Make sure Chrome browser is installed
2. The script automatically downloads the correct ChromeDriver version
3. If automatic download fails, manually install ChromeDriver from https://chromedriver.chromium.org/

### Network Issues

- Ensure you have a stable internet connection
- The script includes delays to be respectful to the server
- If requests fail, the script will continue with other endpoints

### Missing Elements

If the script can't find expected elements:

- The API documentation structure may have changed
- Check the browser console for JavaScript errors
- The script includes fallback selectors for common variations

## Output Example

```markdown
# iCabbi API Documentation

## API Details
- **API URL**: https://api.icabbidispatch.com/icd/
- **APP KEY**: 3ff0c443f405cae2ef723ccece2f8008e6a7a70a
- **SECRET KEY**: 27dd3a9574a6c72be5986aea31554921d687bcef

## Booking Endpoints

### Create Booking

- **Method**: POST
- **Endpoint**: `/booking/create`

#### Parameters
- **phone** (string): Customer phone number
- **pickup_address** (string): Pickup location
- **destination_address** (string): Destination location

#### Try Response

##### Sample Input
```json
{
  "phone": "+441234567890",
  "pickup_address": "123 Main Street, London, UK",
  "destination_address": "Heathrow Airport, London, UK"
}
```

##### Response
```json
{
  "status": "success",
  "booking_id": "12345",
  "estimated_fare": "£45.00"
}
```
```

## Contributing

Feel free to enhance the script by:

- Adding support for more API documentation formats
- Improving sample data generation
- Adding more robust error handling
- Supporting additional authentication methods
